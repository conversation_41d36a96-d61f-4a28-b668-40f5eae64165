# Smart Visual Generation System v2.0

A comprehensive, open-source AI-powered image generation and editing system that runs entirely on your local machine.

## Features

- Text-to-Image Generation using Stable Diffusion XL
- Automatic Object Segmentation with SAM
- Region-based Understanding with Florence-2
- Instruction-based Region Editing
- Face Enhancement and Upscaling
- 2D to 3D Conversion

## System Requirements

- Windows 10/11
- NVIDIA GPU with 8GB+ VRAM
- 16GB+ RAM
- 100GB+ free storage
- Python 3.8+

## Quick Start

1. Clone this repository
2. Run `start.bat`
3. Wait for the setup to complete
4. Access the application at http://localhost:3000

## Manual Setup

1. Create a Python virtual environment:
   ```bash
   python -m venv venv
   ```

2. Activate the virtual environment:
   ```bash
   venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Start the backend server:
   ```bash
   cd backend
   python main.py
   ```

5. Start the frontend development server:
   ```bash
   cd frontend
   npm install
   npm start
   ```

## Project Structure

```
├── backend/             # FastAPI backend
├── frontend/           # React frontend
├── models/            # Downloaded AI models
├── outputs/           # Generated images
├── requirements.txt   # Python dependencies
└── start.bat         # Windows startup script
```

## Model Downloads

The system will automatically download required models on first run. Models are cached in the `models/` directory.

## License

MIT License - See LICENSE file for details

## Support

For issues and feature requests, please use the GitHub issue tracker. 