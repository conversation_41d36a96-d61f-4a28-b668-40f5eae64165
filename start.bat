@echo off
echo Setting up Smart Visual Generation System v2.0...

:: Create virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

:: Activate virtual environment
call venv\Scripts\activate

:: Install requirements
echo Installing requirements...
pip install -r requirements.txt

:: Create necessary directories
if not exist "models" mkdir models
if not exist "outputs" mkdir outputs

:: Set environment variables
set PYTHONPATH=%PYTHONPATH%;%CD%
set MODEL_CACHE_DIR=%CD%\models

:: Start the backend server
echo Starting backend server...
start cmd /k "cd backend && python main.py"

:: Start the frontend (assuming it's built)
echo Starting frontend...
start http://localhost:3000

echo Setup complete! The application should be running now.
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3000 